<ion-header [translucent]="true">
  <ion-toolbar class="main-toolbar">
    <ion-title class="main-title">Permits</ion-title>
    <ion-buttons slot="end" class="header-buttons">
      <ion-button (click)="createNewPermit()" [disabled]="isDissableCreatePermit" class="header-btn">
        <ion-icon slot="icon-only" name="add-circle-outline"></ion-icon>
      </ion-button>
      <ion-button (click)="refreshPermit()" class="header-btn">
        <ion-icon slot="icon-only" name="refresh-outline"></ion-icon>
      </ion-button>
      <ion-button (click)="goToSettings()" class="header-btn">
        <ion-icon slot="icon-only" name="settings-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>

  <!-- Facility Selection -->
  <ion-toolbar class="facility-toolbar">
    <ion-item class="facility-item" mode="ios" [button]="true" id="open-modal" lines="none">
      <ion-icon slot="start" name="business-outline" class="facility-icon"></ion-icon>
      <ion-label *ngIf="userObject">
        <h2>{{userObject?.CURRENT_FACILITY_DESC}}</h2>
        <p>{{userObject?.CURRENT_FACILITY}}</p>
      </ion-label>
      <ion-label *ngIf="!userObject">
        <h2>Select Facility</h2>
      </ion-label>
    </ion-item>
  </ion-toolbar>

  <!-- Search and Filter Section with Collapse Animation -->
  <ion-toolbar class="search-toolbar" [class.hidden]="isSearchBarHidden">
    <div class="search-filter-container">
      <ion-searchbar
        [(ngModel)]="searchTerm"
        (ionInput)="onSearchInput($event)"
        (ionClear)="onSearchClear()"
        placeholder="Search permits..."
        debounce="300"
        class="mobile-searchbar">
      </ion-searchbar>

      <ion-select
        [(ngModel)]="selectedStatuses"
        interface="action-sheet"
        mode="ios"
        placeholder="My Permits"
        multiple="true"
        (ionChange)="onStatusFilterChange($event)"
        class="filter-select">
        <ion-select-option value="All">All</ion-select-option>
        <ion-select-option value="Assigned">My Permits</ion-select-option>
        <ion-select-option value="OPEN">Open</ion-select-option>
        <ion-select-option value="IN_REVIEW">In Review</ion-select-option>
        <ion-select-option value="APPROVED">Approved</ion-select-option>
        <ion-select-option value="ISSUED">Issued</ion-select-option>
        <ion-select-option value="CLOSED">Closed</ion-select-option>
        <ion-select-option value="CANCELLED">Cancelled</ion-select-option>
      </ion-select>
    </div>
  </ion-toolbar>
</ion-header>

<div class="mobile-content-wrapper" (scroll)="onScroll($event)">
  <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content pullingIcon="arrow-down-circle-outline" pullingText="Pull to refresh"
      refreshingSpinner="lines">
    </ion-refresher-content>
  </ion-refresher>

  <!-- Modern Card Layout -->
  <div class="permits-container">
    <!-- Shimmer Loading Cards -->
    <div *ngIf="isLoading" class="shimmer-card" *ngFor="let item of shimmerItems; trackBy: trackByIndex">
      <div class="shimmer-header">
        <div style="display: flex; align-items: center;">
          <div class="shimmer-icon shimmer"></div>
          <div class="shimmer-permit-number shimmer"></div>
        </div>
        <div class="shimmer-status shimmer"></div>
      </div>
      <div class="shimmer-title shimmer"></div>
      <div class="shimmer-detail">
        <div class="shimmer-detail-icon shimmer"></div>
        <div class="shimmer-detail-text medium shimmer"></div>
      </div>
      <div class="shimmer-detail">
        <div class="shimmer-detail-icon shimmer"></div>
        <div class="shimmer-detail-text long shimmer"></div>
      </div>
      <div class="shimmer-detail">
        <div class="shimmer-detail-icon shimmer"></div>
        <div class="shimmer-detail-text short shimmer"></div>
      </div>
      <div class="shimmer-detail">
        <div class="shimmer-detail-icon shimmer"></div>
        <div class="shimmer-detail-text medium shimmer"></div>
      </div>
    </div>

    <!-- Actual Permit Cards -->
    <div class="permit-card"
         *ngFor="let permit of permitFilteredList; index as i"
         (click)="permit.SYNC_STATUS == 0 ? showPermitDetails(permit) : ''; $event.stopPropagation();"
         [ngClass]="isPermitIsExpired(permit) ? 'expired-card': 'normal-card'"
         [style.display]="isLoading ? 'none' : 'block'">

      <!-- Permit Header: Icon + Permit Number + Status -->
      <div class="permit-header">
        <div class="permit-header-left">
          <i *ngIf="permit.permitTypeInfo?.ICON"
             class="fas fa-{{permit.permitTypeInfo.ICON}} permit-type-icon"
             [style.color]="getColorHex(permit.permitTypeInfo.COLOR)"></i>
          <i *ngIf="!permit.permitTypeInfo?.ICON"
             class="fas fa-question-circle permit-type-icon"
             style="color: #3880ff;"></i>
          <span class="permit-number">{{permit.PERMIT_NO}}</span>
        </div>
        <div class="status-badge" [ngClass]="GetProgressPercentCssClass(permit.STATUS)">
          {{getLabelBasedOnStatus(permit.STATUS)}}
        </div>
      </div>

      <!-- Permit Description -->
      <div class="permit-title">
        <h3>{{permit.DESCRIPTION}}</h3>
      </div>

      <!-- Permit Details -->
      <div class="permit-details">
        <div class="detail-item">
          <i class="fas fa-toolbox detail-icon"></i>
          <span>{{permit.TAG | GetTagNamePipe | async}}</span>
        </div>

        <div class="detail-item">
          <i class="fas fa-calendar-alt detail-icon"></i>
          <span>{{permit.PERMIT_DATE | GetFullDateByTimestampPipe | async}}</span>
        </div>

        <div class="detail-item">
          <i class="fas fa-calendar-times detail-icon"></i>
          <span [ngClass]="{'expired-date': isPermitIsExpired(permit)}">{{permit.EXPIRY_DATE | GetFullDateByTimestampPipe | async}}</span>
          <div *ngIf="isPermitIsExpired(permit)" class="expired-badge">Expired</div>
        </div>

        <div class="detail-item">
          <i class="fas fa-user detail-icon"></i>
          <span>{{permit.REQUESTED_BY | GetRequestedByNamePipe | async}}, {{permit.REQUESTED_ON | date}}</span>
        </div>

        <div class="detail-item" *ngIf="permit.IS_EXTENDED == 'true'">
          <i class="fas fa-clock detail-icon"></i>
          <span>Extended to {{permit.EXTENSION_DATE | GetFullDateByTimestampPipe | async}}</span>
        </div>
      </div>

      <!-- Action Section - Only show if there are actions available -->
      <div class="action-section" *ngIf="hasAvailableActions(permit)">
        <!-- Sync Status Indicators -->
        <div *ngIf="permit.SYNC_STATUS == 2" class="sync-status">
          <img style="height:32px;width:32px;" src="assets/server-upload-2.png">
          <span class="sync-text">Synced</span>
        </div>

        <div *ngIf="permit.SYNC_STATUS == 1" class="sync-status">
          <img style="height:32px;width:32px;" src="assets/load.gif">
          <span class="sync-text">Syncing...</span>
        </div>

        <div *ngIf="permit.SYNC_STATUS == 3" class="sync-status error">
          <ion-button color="danger" fill="clear" (click)="showErrorMessage(permit); $event.stopPropagation();">
            <i class="fal fa-exclamation-circle fa-lg"></i>
          </ion-button>
          <span class="sync-text">Error</span>
        </div>

        <!-- Action Buttons -->
        <div *ngIf="permit.SYNC_STATUS == 0" class="action-buttons">
          <!-- OPEN Status - Submit for Review -->
          <ion-button *ngIf="shouldShowSubmitButton(permit)"
                      class="action-btn primary-btn"
                      [disabled]="isActionButtonDissabled('OPEN') || isPermitIsExpired(permit)"
                      (click)="permitAction(permit.STATUS, permit, false);$event.stopPropagation();">
            Submit for Review
          </ion-button>

          <!-- IN_REVIEW Status - Approve -->
          <ion-button *ngIf="shouldShowApproveButton(permit)"
                      class="action-btn approve-btn"
                      [disabled]="isActionButtonDissabled('IN_REVIEW') || isPermitIsExpired(permit)"
                      (click)="permitAction('IN_REVIEW', permit, false);$event.stopPropagation();">
            Approve
          </ion-button>

          <!-- APPROVED Status - Issue -->
          <ion-button *ngIf="shouldShowIssueButton(permit)"
                      class="action-btn issue-btn"
                      [disabled]="isActionButtonDissabled('ISSUED') || isPermitIsExpired(permit)"
                      (click)="permitAction('APPROVED', permit, false);$event.stopPropagation();">
            Issue
          </ion-button>

          <!-- ISSUED Status - Revise -->
          <ion-button *ngIf="shouldShowReviseButton(permit)"
                      class="action-btn revise-btn"
                      [disabled]="isActionButtonDissabled('REVISE') || isPermitIsExpired(permit)"
                      (click)="permitAction('REVISE', permit, false);$event.stopPropagation();">
            Revise
          </ion-button>

          <!-- ISSUED Status - Close -->
          <ion-button *ngIf="shouldShowCloseButton(permit)"
                      class="action-btn close-btn"
                      [disabled]="isActionButtonDissabled('CLOSE') || isPermitIsExpired(permit)"
                      (click)="permitAction('CLOSED', permit, false);$event.stopPropagation();">
            Close
          </ion-button>

          <!-- Cancel Button -->
          <ion-button *ngIf="shouldShowCancelButton(permit)"
                      class="action-btn cancel-btn"
                      [disabled]="isActionButtonDissabled('CANCELLED')"
                      (click)="cancelPermit(permit);$event.stopPropagation();">
            Cancel
          </ion-button>
        </div>
      </div>
    </div>
  </div>
  <!-- Elegant Zero Data Screen -->
  <div *ngIf="!isLoading && permitFilteredList.length == 0" class="zero-data-container">
    <div class="zero-data-illustration">
      <ion-icon name="document-text-outline"></ion-icon>
    </div>
    <h2 class="zero-data-title">No Permits Found</h2>
    <p class="zero-data-subtitle">
      There are no permits to display at the moment. Create a new permit to get started.
    </p>
    <ion-button
      *ngIf="!isDissableCreatePermit"
      class="zero-data-action"
      (click)="createNewPermit()">
      <ion-icon name="add-outline"></ion-icon>
      Create New Permit
    </ion-button>
  </div>

  <div style="text-align: center;margin-bottom: 10px" *ngIf="isShowLoadMoreButton && permitFilteredList.length > 0 && !isLoading">
    <ion-button fill="outline" size="small" (click)="loadMore(); $event.stopPropagation();">Load More</ion-button>
  </div>
</div>

<ion-modal #modal trigger="open-modal" [presentingElement]="presentingElement">
    <ng-template>
      <ion-header>
        <ion-toolbar class="main-toolbar">
          <ion-title class="main-title">Select Facility</ion-title>
          <ion-buttons slot="end">
            <ion-button class="header-btn" (click)="modal.dismiss()">Close</ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <div class="facility-modal-content">
        <ion-list>
          <ion-item [class.selected]="facility.FACILITY_ID === userFacility" mode="ios" [button]="true"
            *ngFor="let facility of facilitiesList; index as i" (click)="onFacilityChange(facility)">
            <ion-icon slot="start" name="business-outline"></ion-icon>
            <ion-label>
              <h2>{{facility.NAME}}</h2>
              <p>{{facility.FACILITY_ID}}</p>
            </ion-label>
          </ion-item>
        </ion-list>
      </div>
    </ng-template>
  </ion-modal>