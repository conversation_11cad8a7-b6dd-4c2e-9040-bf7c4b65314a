// Header Styles
.main-toolbar {
    --background: #ffffff;
    --color: #333333;
    --border-width: 0 0 1px 0;
    --border-color: #e0e0e0;
    --min-height: 56px;
}

.main-title {
    font-size: 20px;
    font-weight: 600;
    color: #333333;
}

.header-buttons {
    .header-btn {
        --color: #666666;
        --background: transparent;
        --border-radius: 50%;
        --padding-start: 8px;
        --padding-end: 8px;
        margin-left: 4px;

        ion-icon {
            font-size: 24px;
        }

        &:hover {
            --background: #f0f0f0;
        }
    }
}

.facility-toolbar {
    --background: #f8f9fa;
    --border-width: 0 0 1px 0;
    --border-color: #e0e0e0;
    --min-height: 50px;
}

.facility-item {
    --background: transparent;
    --color: #333333;
    --padding-start: 16px;
    --padding-end: 16px;

    .facility-icon {
        color: #007bff; // Blue color as in the picture
        margin-right: 12px;
    }

    ion-label {
        h2 {
            font-size: 14px;
            font-weight: 500;
            margin: 0;
        }

        p {
            font-size: 12px;
            color: #666666;
            margin: 2px 0 0 0;
        }
    }
}

.search-toolbar {
    --background: #ffffff;
    --border-width: 0 0 1px 0;
    --border-color: #e0e0e0;
    --min-height: 60px;
    padding: 8px 16px;
    transition: all 0.3s ease-in-out;
    transform: translateY(0);
    opacity: 1;
    max-height: 80px;
    overflow: hidden;

    &.hidden {
        transform: translateY(-100%);
        opacity: 0;
        max-height: 0;
        padding: 0 16px;
    }
}

.search-filter-container {
    display: flex;
    gap: 8px;
    align-items: center;
}

.mobile-searchbar {
    flex: 2; // Reduce search bar width (2/3 of available width)
    --background: #f8f9fa;
    --border-radius: 20px;
    --box-shadow: none;
    --placeholder-color: #999999;
    --icon-color: #666666;
    --color: #333333;
    --clear-button-color: #666666;

    .searchbar-input {
        --padding-start: 16px !important;
        --padding-end: 16px !important;
    }
}

.filter-select {
    flex: 1; // Give more space to filter dropdown (1/3 of available width)
    min-width: 100px;
    max-width: 150px;
    --background: #f8f9fa;
    --border-radius: 20px;
    --padding-start: 12px;
    --padding-end: 8px;
    --placeholder-color: #666666;
    --color: #333333;
    font-size: 13px;
}

// Card Container
.permits-container {
    padding: 16px;
    background-color: #f5f5f5;
    height: auto;
    position: relative;
    top: 0;
    margin-top: 0;
    transform: none;
}

// Mobile Content Wrapper - Enable scrolling without ion-content issues
.mobile-content-wrapper {
    height: calc(100vh - 56px); // Full height minus header
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    background: #f5f5f5;
    position: relative;
}

// Facility Modal Content Wrapper
.facility-modal-content {
    height: calc(100vh - 120px); // Full height minus header and modal padding
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    background: #ffffff;
    position: relative;
}

// Fix content alignment for mobile - scoped to this page only
:host ion-content {
    --background: #f5f5f5 !important;
    // Override global gradient background
    background: #f5f5f5 !important;
}

// Fix content alignment for mobile - minimal overrides
:host {
    // Override global empty-data-info styles that cause bottom alignment
    .empty-data-info {
        padding-top: 40px !important; // Override global 25vh
        position: static !important;
        transform: none !important;
        margin-top: 0 !important;
    }
}

// Modern Card Styles
.permit-card {
    background: white;
    border-radius: 12px;
    margin-bottom: 16px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }

    &.expired-card {
        background: white;
        border-color: #e0e0e0;
        border-width: 1px;
    }

    &.normal-card {
        background: white;
        border-color: #e0e0e0;
    }
}

// Permit Header (Icon + Number + Status)
.permit-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 0;
}

.permit-header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.permit-type-icon {
    font-size: 20px;
    width: 24px;
    text-align: center;
}

.permit-number {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

// Status Badge - Match web UI colors
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    color: white;

    &.orange {
        background-color: #f59e0b;
        color: white;
    }

    &.light-Green {
        background-color: #10b981;
        color: white;
    }

    &.darak-Green {
        background-color: #047857;
        color: white;
    }

    &.light-Grey {
        background-color: #ef4444;
        color: white;
    }
}

// Permit Title
.permit-title {
    margin-bottom: 16px;

    h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        color: #333;
        line-height: 1.4;
    }
}

// Permit Details
.permit-details {
    margin-bottom: 16px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 14px;
    color: #555;

    &:last-child {
        margin-bottom: 0;
    }
}

.detail-icon {
    font-size: 14px;
    width: 16px;
    text-align: center;
    color: #666;
}

// Action Section - Only shows when there are actions
.action-section {
    border-top: 1px solid #e0e0e0;
    padding-top: 12px;
    margin-top: 12px;
}

.sync-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px;

    .sync-text {
        font-size: 12px;
        color: #666;
    }

    &.error {
        .sync-text {
            color: #dc3545;
        }
    }
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.action-btn {
    --border-radius: 8px;
    --padding-top: 8px;
    --padding-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
    margin: 0;
    height: auto;

    &.primary-btn {
        --background: #007bff;
        --background-hover: #0056b3;
        --color: white;
    }

    &.approve-btn {
        --background: #28a745;
        --background-hover: #218838;
        --color: white;
    }

    &.issue-btn {
        --background: #17a2b8;
        --background-hover: #138496;
        --color: white;
    }

    &.revise-btn {
        --background: #ffc107;
        --background-hover: #e0a800;
        --color: #212529;
    }

    &.close-btn {
        --background: #6c757d;
        --background-hover: #545b62;
        --color: white;
    }

    &.cancel-btn {
        --background: transparent;
        --color: #dc3545;
        --border-color: #dc3545;
        --border-width: 1px;
        --border-style: solid;

        &:hover {
            --background: #dc3545;
            --color: white;
        }
    }
}

// Expired Badge and Date Styling (smaller with border only)
.expired-badge {
    background-color: transparent;
    border: 1px solid #ef4444;
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
    color: #ef4444;
    text-align: center;
    letter-spacing: 0.2px;
    display: inline-block;
    margin-left: 6px;
    flex-shrink: 0;
}

.expired-date {
    color: #ef4444 !important;
}

// Empty State
.empty-data-info {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-size: 16px;
    margin: 0;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

// Legacy Progress Bar Classes (for compatibility)
.orange {
    --progress-background: orange;
}

.light-Green {
    --progress-background: rgb(105, 226, 105);
}

.darak-Green {
    --progress-background: rgb(13, 172, 13);
}

.light-Grey {
    --progress-background: indianred;
}
   