// .toolbar {
//   --padding-top: 0px !important;
//   --padding-bottom: 0px !important;
//   --padding-start: 0px !important;
//   --padding-end: 0px !important;
//   --min-height: 35px !important;
//   --border-color: transparent !important;
// }

ion-toolbar {
  padding-inline: 10px;
  padding-top: 0px;
  padding-bottom: 0px;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 0.0125em;
}

.refreshButton {
  // --border-color: #868686 !important;
  --border-width: 1px !important;
  --border-radius: 8px !important;
  font-weight: 600 !important;
}

.row-hover:hover {
  background: #f6f7f9;
}

.row-hover {
  color: #495057;
  border-color: #dee2e6;
  // padding: 0.2rem;
  border-bottom: 1px solid #dee2e6;
}

.m-l-10 {
  margin-left: 10px;
}


.actionButtonClass{
  --padding-start:16px !important;min-height: 35px !important; --highlight-color: transparent !important;
}

.approveActionButton{
  border-color: green ;color: green;  font-size: 13px; margin-right: 15px;margin-left: 25px;
}

.rejectActionButton{
  border-color: red ;color: red;  font-size: 13px;margin-right: 15px;margin-left: 5px;
}

.disabled-row {
  pointer-events: none; /* Disables clicks */
  opacity: 0.6; /* Makes it look disabled */
  background-color: #f5f5f5; /* Light gray background */
}

// ion-segment-button::part(indicator-background) {
//   background: #08a391;
// }

// /* Material Design styles */
// ion-segment-button.md::part(native) {
//   color: #000;
// }

// .segment-button-checked.md::part(native) {
//   color: #08a391;
// }

// ion-segment-button.md::part(indicator-background) {
//   height: 4px;
// }

// /* iOS styles */
// ion-segment-button.ios::part(native) {
//   color: #08a391;
// }

// .segment-button-checked.ios::part(native) {
//   color: #fff;
// }

// ion-segment-button.ios::part(indicator-background) {
//   border-radius: 20px;
// }
.test {
  .toast-message {
    text-align: center;
  }
}

// ion-header {
//   background: var(--ion-color-primary);
// }

.header-grid {
  --ion-grid-padding: 0 !important;

  .header-column {
    --ion-grid-column-padding: 0 !important;
    color: var(--ion-color-light);

    .header-column-div {
      text-align: center !important;
      padding-bottom: 5px !important;
      font-weight: bold;
      color: #fff;
    }
  }
}

ion-checkbox {
  --checkmark-color: var(--ion-checkbox-check);
  --background-checked: var(--ion-color-light);
  --border-color-checked: var(--ion-color-light);
}

.header-text {
  color: #fff;
  --color: white !important;
  --ion-color-base-rgb: var(--ion-color-header-base-color, #fff) !important;
}

.header-text-style {
  font-weight: bold;
  border: 1px solid white;
  border-bottom-left-radius: 6px;
  border-top-left-radius: 6px;
  margin: 0;
}

.form-control {
  background-color: var(--ion-input-bg-color) !important;
  color: var(--ion-input-color) !important;
  border: 1px solid var(--ion-input-border-color) !important;
}

.header-toolbar {
  --background: var(--ion-color-primary);
  --color: var(--ion-color-primary-contrast);

  ion-icon {
    color: var(--ion-color-primary-contrast);
  }
}

.alertCount {
  --color: #303f9f;
  --background: #ffffff;
  transform: translate(5px, 0px);
}

.notMobileViewAlertCount {
  --color: #303f9f;
  --background: #ffffff;
  transform: translate(-20px, -10px);
}

.mobileViewAlertCount {
  --color: #303f9f;
  --background: #ffffff;
  zoom: 0.8;
  transform: translate(-8px, 0px);
}

// ion-content {
//   --background: var(--ion-color-light);
// }

.blur-content {
  filter: blur(10px);
}

.display-middle {
  position: absolute;
  top: 160px;
}

#splitBtn ion-icon {
  zoom: 1.2;
}

#gaugeArea {
  border: 1px solid grey;
  background: white;
  position: fixed;
  bottom: 0;
  right: 15px;
}

#gaugeClose {
  background-color: transparent;
  --background: transparent;
  --color: black;
  right: 10px;
  position: fixed;
  bottom: 91px;
}

#badge {
  height: 100%;
  padding-left: 8px;
  padding-top: 8px;
  border-radius: 10px;
}

.sdc-spinner {
  display: none;
  pointer-events: all;
  z-index: 99999;
  border: none;
  margin-top: 80px;
  padding: 0px;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
  cursor: wait;
  position: fixed;
}

.sk-chase {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  animation: sk-chase 2.5s infinite linear both;
}

.sk-chase-dot {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  animation: sk-chase-dot 2s infinite ease-in-out both;
}

.sk-chase-dot:before {
  content: "";
  display: block;
  width: 25%;
  height: 25%;
  background-color: #303f9f;
  border-radius: 100%;
  animation: sk-chase-dot-before 2s infinite ease-in-out both;
}

.sk-chase-dot:nth-child(1) {
  animation-delay: -1.1s;
}

.sk-chase-dot:nth-child(2) {
  animation-delay: -1s;
}

.sk-chase-dot:nth-child(3) {
  animation-delay: -0.9s;
}

.sk-chase-dot:nth-child(4) {
  animation-delay: -0.8s;
}

.sk-chase-dot:nth-child(5) {
  animation-delay: -0.7s;
}

.sk-chase-dot:nth-child(6) {
  animation-delay: -0.6s;
}

.sk-chase-dot:nth-child(1):before {
  animation-delay: -1.1s;
}

.sk-chase-dot:nth-child(2):before {
  animation-delay: -1s;
}

.sk-chase-dot:nth-child(3):before {
  animation-delay: -0.9s;
}

.sk-chase-dot:nth-child(4):before {
  animation-delay: -0.8s;
}

.sk-chase-dot:nth-child(5):before {
  animation-delay: -0.7s;
}

.sk-chase-dot:nth-child(6):before {
  animation-delay: -0.6s;
}

@keyframes sk-chase {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes sk-chase-dot {
  80%,
  100% {
    transform: rotate(360deg);
  }
}

@keyframes sk-chase-dot-before {
  50% {
    transform: scale(0.4);
  }

  100%,
  0% {
    transform: scale(1);
  }
}

.alertDiv {
  position: -webkit-sticky;
  position: -moz-sticky;
  position: -ms-sticky;
  position: -o-sticky;
  position: sticky;
  z-index: 1;
  top: 0;
}
// ion-title.ios {
//   width: 75%;
// }
#formio {
  padding: 5px !important;
}

.vertical-line {
  margin: 0px 0px 0px 10px;
}

.uploadImages {
  overflow-x: auto;
  flex-wrap: nowrap;
  display: flex;
}

.uploadImages > ion-card {
  min-width: 100px;
  border: 1px solid #dbdbdb;
  padding: 4px;
  font-weight: lighter;
  margin: 8px 4px;
  height: 100px;
  border-radius: 5px;
  box-shadow: none;
  width: 100px;
}

.uploadImages > ion-card > img {
  min-width: 100%;
  height: 100px;
  object-fit: cover;
}

.uploadButton {
  input {
    display: none;
  }
  --background: traansparent;
  --box-shadow: none;
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
}
$sample-gap: 20px;

// :host {
//     display: block;
//     padding: $sample-gap;
// }

.sample-split,
.sample-step-actions {
    display: flex;
    gap: $sample-gap;
}

.sample-step-actions {
    margin-top: $sample-gap * 2;
}

.sample-split {
    > * {
        max-width: 380px;
        flex: 1;
    }

    margin-bottom: $sample-gap;
}
.isDisable{
  pointer-events: none !important;
}

.date-control {
  --border-color: var(--ion-color-step-300, #b3b3b3);
  --border-radius: 4px;
  --border-width: 1px;
  --min-height: 40px;
}

.partners-header-row {
  color: #50596c;
  font-weight: 600;
  border-bottom: 1px solid #dee2e6;
}

.readonly-item {
  pointer-events: none; /* Prevent interaction */
  opacity: 1; /* Ensure the text is not greyed out */
  color: #50596c; /* Set text color to black */
}

.readonly-item ion-label,
.readonly-item ion-text {
  color: #50596c !important; /* Ensure labels and text are black */
}


.selected-tab {
  --background-color: steelblue !important;
  --color: steelblue !important; /* Ensure the text color remains white */
}

// Responsive padding for content areas
.content-area-desktop {
  padding: 16px;
}

.content-area-mobile {
  padding: 16px 4px;
}

// Special case for form content - no horizontal padding on mobile
.content-area-mobile.form-content {
  padding: 0 4px;
}

// Shimmer Loading Animation
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

// Shimmer Card Skeleton for Details Section
.shimmer-details-card {
  background: white;
  border-radius: 12px;
  margin-bottom: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 1px solid #e0e0e0;
}

.shimmer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.shimmer-title {
  width: 60%;
  height: 24px;
  border-radius: 4px;
  background: #e0e0e0;
}

.shimmer-status {
  width: 80px;
  height: 28px;
  border-radius: 14px;
  background: #e0e0e0;
}

.shimmer-detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;

  .shimmer-icon {
    width: 20px;
    height: 20px;
    border-radius: 2px;
    background: #e0e0e0;
    margin-right: 12px;
  }

  .shimmer-text {
    height: 18px;
    border-radius: 4px;
    background: #e0e0e0;
    flex: 1;

    &.short {
      width: 40%;
    }

    &.medium {
      width: 70%;
    }

    &.long {
      width: 90%;
    }
  }
}

// Shimmer for Partners Section
.shimmer-partners-section {
  background: white;
  border-radius: 12px;
  margin-bottom: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 1px solid #e0e0e0;
}

.shimmer-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .shimmer-section-title {
    width: 150px;
    height: 20px;
    border-radius: 4px;
    background: #e0e0e0;
  }

  .shimmer-edit-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #e0e0e0;
  }
}

.shimmer-table-header {
  display: flex;
  padding: 8px 16px;
  margin-bottom: 8px;
  background: #f8f9fa;
  border-radius: 8px;

  .shimmer-header-cell {
    height: 16px;
    border-radius: 4px;
    background: #e0e0e0;
    margin-right: 16px;

    &.seq {
      width: 40px;
    }

    &.role {
      width: 80px;
    }

    &.user {
      width: 120px;
    }

    &.actions {
      width: 100px;
    }
  }
}

.shimmer-table-row {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  margin-bottom: 8px;
  border-bottom: 1px solid #f1f5f9;

  .shimmer-cell {
    height: 16px;
    border-radius: 4px;
    background: #e0e0e0;
    margin-right: 16px;

    &.seq {
      width: 20px;
    }

    &.role {
      width: 70px;
    }

    &.user {
      width: 100px;
    }

    &.actions {
      width: 80px;
    }
  }
}

// Shimmer for Form Section
.shimmer-form-container {
  background: white;
  border-radius: 12px;
  margin: 16px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 1px solid #e0e0e0;
}

.shimmer-form-header {
  margin-bottom: 24px;

  .shimmer-form-title {
    width: 200px;
    height: 28px;
    border-radius: 6px;
    background: #e0e0e0;
  }
}

.shimmer-form-field {
  margin-bottom: 20px;

  .shimmer-label {
    width: 120px;
    height: 16px;
    border-radius: 4px;
    background: #e0e0e0;
    margin-bottom: 8px;
  }

  .shimmer-input {
    width: 100%;
    height: 40px;
    border-radius: 8px;
    background: #e0e0e0;
  }

  .shimmer-textarea {
    width: 100%;
    height: 80px;
    border-radius: 8px;
    background: #e0e0e0;
  }
}

.shimmer-form-group {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;

  .shimmer-form-field {
    flex: 1;
    margin-bottom: 0;
  }
}