/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";
@import "swiper/css";

/* Angular DateTime Picker Styles */
@import "@danielmoncada/angular-datetime-picker/assets/style/picker.min.css";

.full-screen-modal {
  --width: 100% !important;
  --height: 100% !important;
}

ion-segment-button,
ion-button {
  text-transform: none !important;
}

ion-select-options{
  max-height: 200px; /* Set the maximum height */
  overflow-y: auto;  /* Enable vertical scrolling */
}

.sc-ion-modal-md-h {
  --backdrop-opacity: var(--ion-backdrop-opacity, 0.4) !important;
}

.sc-ion-modal-ios-h {
  --backdrop-opacity: var(--ion-backdrop-opacity, 0.4) !important;
}

ion-modal.modal-default.show-modal:last-of-type {
  --box-shadow: 0 28px 48px rgba(0, 0, 0, 0.4) !important;
  --backdrop-opacity: var(--ion-backdrop-opacity, 0.32) !important;
}

.newStakeHolderRolePopup {
  --height: 75% !important;
  --width: 65% !important;
  --height: auto !important;
  --width: 50% !important;
}

.newFormDisplayPopup {
  --height: 90% !important;
  --width: 85% !important;
}


.rejectReason {
--height: auto !important;
--width: 40%  !important;
}

.new-permit-modal {
  --height: auto !important;
  --width: 70% !important;
}

.select-disabled {
  opacity: 1 !important;
  cursor: not-allowed !important;
  pointer-events: auto !important;
}

.empty-data-info {
  text-align: center;
  color: #134373;
  padding-top: 25vh;
  font-size: 16px;
  font-weight: 550;
}

.add-agent-modal {
  --height: auto !important;
  --width: 75% !important;
}

.add-update-modal {
  --height: auto !important;
  --width: 45% !important;
}

.selectClearOption {
  font-size: 12px !important;
}

.master-data-info-modal {
  --height: auto !important;
  --width: 55% !important;
}

.filter-reports-modal {
  --width: 25% !important;
  --height: auto !important;
}

.edit-details-modal {
  --width: 45% !important;
  --max-height: 80% !important;
}

.edit-agents-modal {
  --width: 40% !important;
  --height: 260px !important;
}

.edit-validity-modal {
  --width: 45% !important;
  --height: 260px !important;
}

.edit-comments-modal {
  --width: 45% !important;
  --height: 320px !important;
}

.addOrUpdateFacility {
  --width: 45% !important;
  --height: auto !important;
  --max-height: 90% !important;
  --border-radius: 4px !important;
}

/* Ensure the modal content doesn't overflow and footer is visible */
.addOrUpdateFacility .modal-wrapper {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 4px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.chart-legend {
  display: inline !important;
  padding: 0 !important;
  width: auto !important;
}

.ngx-charts {
  float: unset !important;
}

.ngx-charts-outer {
  max-height: 200px !important;
  height: 200px !important;
}

.chart-legend .legend-labels {
  text-align: center !important;
}

.chart-legend .legend-wrap {
  width: calc(100% - 0px) !important;
}

.star-icon {
  font-size: 22px; /* Adjust star size */
  color: gold; /* Star color */
  cursor: pointer; /* Pointer cursor for clickable stars */
  margin-right: 3px; /* Spacing between stars */
}

.rating-display {
  display: flex;
  align-items: center;
}

.rating-edit {
  display: flex;
  align-items: center;
}


input[type="password"]::-ms-reveal,
input[type="password"]::-ms-clear {
  display: none;
}

input[type="password"]::-webkit-credentials-auto-fill-button {
  display: none !important;
}

input[type="password"]::-webkit-input-decoration-container {
  display: none !important;
}

.header {
  text-align: center;
  padding: 20px;
  background-color: #f8f9fa;
}

.header h1 {
  font-size: 24px;
  margin-bottom: 10px;
}

.header h2 {
  font-size: 20px;
  margin-bottom: 20px;
}

.stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
}

.stat {
  text-align: center;
}

.stat p {
  font-size: 18px;
  font-weight: bold;
}

.stat span {
  font-size: 14px;
  color: #6c757d;
}

.search-bar {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background-color: #f8f9fa;
}

.search-bar ion-searchbar {
  flex: 1;
  margin-right: 10px;
}

ion-list {
  padding: 10px;
}

ion-item {
  --background: #ffffff;
  margin-bottom: 0px;
  border-radius: 0px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

ion-item ion-label {
  font-size: 16px;
}

ion-item ion-button {
  margin-left: 10px;
}

// Global background styles
ion-content {
  --background: linear-gradient(to bottom, rgba(230, 230, 250, 0.6) 0%, rgba(230, 230, 250, 0.2) 100%);
}

/* Success toast styling */
.success-toast {
  font-weight: 500;
  text-align: center;
  --border-radius: 8px;
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  --min-height: 60px;
  --width: 300px;
  --background: var(--ion-color-success-shade);
  --color: white;
}

.success-toast::part(button) {
  color: white;
  font-weight: bold;
}

/* Boundary map modal styling */
.boundary-map-modal {
  --width: 90%;
  --height: 770px; /* Increased height by ~40% from 550px */
  --border-radius: 8px;
  --box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.boundary-map-modal::part(content) {
  border-radius: 8px;
  overflow: hidden;
}

.boundary-map-modal ion-content {
  --background: white;
}

/* Style the footer to ensure it's visible */
.boundary-map-modal ion-footer {
  position: relative;
  bottom: 0;
}

/* Ensure buttons are properly sized */
.boundary-map-modal ion-button {
  margin: 0 4px;
}

/* Location picker map modal styling */
.map-modal {
  --width: 80%;
  --height: 770px; /* Increased height by ~40% from 550px */
  --border-radius: 4px;
  --box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.map-modal .modal-wrapper {
  border-radius: 4px;
  overflow: hidden;
}

/* Skills tooltip popover styling */
.skills-tooltip-popover {
  --width: auto !important;
  --min-width: 450px !important;
  --max-width: 600px !important;
  --max-height: 80vh !important;
}

.skills-tooltip-popover .popover-content {
  width: auto !important;
  min-width: 450px !important;
  max-width: 600px !important;
  max-height: 80vh !important;
  overflow-y: auto !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
  padding: 0 !important;

  // Ensure no white patches on corners
  > * {
    border-radius: inherit;
  }
}

.skills-tooltip-popover .popover-wrapper {
  position: fixed !important;
  z-index: 20000 !important;
}

/* Responsive adjustments for skills tooltip */
@media (max-width: 768px) {
  .skills-tooltip-popover {
    --min-width: 350px !important;
    --max-width: 450px !important;
  }

  .skills-tooltip-popover .popover-content {
    min-width: 350px !important;
    max-width: 450px !important;
  }
}

@media (max-width: 480px) {
  .skills-tooltip-popover {
    --min-width: 300px !important;
    --max-width: 380px !important;
  }

  .skills-tooltip-popover .popover-content {
    min-width: 300px !important;
    max-width: 380px !important;
    margin: 10px !important;
  }
}

